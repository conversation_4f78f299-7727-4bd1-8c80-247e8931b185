import axios from 'axios';
import toast from 'react-hot-toast';
import {
  cleanToken,
  getAccessToken,
  getRefreshToken,
  setAccessToken,
  setRefreshToken,
} from '../local-storage/access-local-storage';
import { history } from '@/pages/Auth';

const instance = axios.create({
  baseURL: import.meta.env.VITE_API_SERVER_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to fix URL construction and add auth
instance.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Fix double slashes in URL paths
    if (config.url && config.baseURL) {
      // Remove leading slash from URL if present
      if (config.url.startsWith('/')) {
        config.url = config.url.substring(1);
      }

      // Remove trailing slash from baseURL if present
      const baseURL = config.baseURL.endsWith('/')
        ? config.baseURL.slice(0, -1)
        : config.baseURL;

      // Construct the final URL properly
      const finalUrl = `${baseURL}/${config.url}`;

      // Debug log the fixed URL
      console.log('API URL:', finalUrl);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  (res) => res,
  async (err) => {
    const originalConfig = err.config;

    if (originalConfig.url !== 'admin/auth/login' && err.response) {
      if (err.response.status === 401 && !originalConfig._retry) {
        originalConfig._retry = true;

        try {
          const refreshToken = getRefreshToken();
          if (refreshToken) {

            const response = await instance.post('admin/auth/refresh', { refreshToken });
            setRefreshToken(response.data.refreshToken);
            setAccessToken(response.data.accessToken);

            instance.defaults.headers.common.Authorization = `Bearer ${response.data.accessToken}`;
            originalConfig.headers.Authorization = `Bearer ${response.data.accessToken}`;

            return instance(originalConfig);
          }
        } catch (_error: unknown) {
          cleanToken();
          toast.error('Session expired. Please log in again.');
          history.push('/');
          window.location.href = '/';
          return Promise.reject(_error);
        }
      }
    }
    return Promise.reject(err);
  }
);

export default instance;
