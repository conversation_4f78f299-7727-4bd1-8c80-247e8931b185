import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from 'react-query';
import { TariffUpload, HotelRoom, ExtractedTariffData } from '@/types/types';
import { Button } from '@/components/ui/button';
import { AlertCircle, Check, FileText, Info, Upload, X } from 'lucide-react';
import { format } from 'date-fns';
import { handleFileUpload } from '@/utils/api-functions/upload-file';
import {
  fetchHotelTariffs,
  createTariffUpload,
  updateTariffStatus,
  deleteTariffUpload
} from '@/utils/api-functions/tariff-upload';
import { 
  extractTariffFromPDF, 
  checkTariffExtractionService 
} from '@/utils/api-functions/tariff-extraction';
// Removed unused import
import toast from 'react-hot-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import TariffComparison from './TariffComparison';

// Custom Alert Components since we don't have the imported ones
type AlertVariant = 'default' | 'destructive';

interface AlertProps {
  children: React.ReactNode;
  className?: string;
  variant?: AlertVariant;
}

interface AlertTitleProps {
  children: React.ReactNode;
  className?: string;
}

interface AlertDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({ children, className = "", variant = "default" }) => {
  const baseClasses = "relative w-full rounded-lg border p-4 mb-4";
  const variantClasses: Record<AlertVariant, string> = {
    default: "bg-white border-gray-200",
    destructive: "bg-red-50 border-red-200 text-red-700",
  };

  return (
    <div
      role="alert"
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </div>
  );
};

const AlertTitle: React.FC<AlertTitleProps> = ({ children, className = "" }) => (
  <h5 className={`mb-1 font-medium ${className}`}>{children}</h5>
);

const AlertDescription: React.FC<AlertDescriptionProps> = ({ children, className = "" }) => (
  <div className={`text-sm ${className}`}>{children}</div>
);

interface TariffUploadListProps {
  hotelId: string;
  rooms: HotelRoom[];
}

const TariffUploadList: React.FC<TariffUploadListProps> = ({ hotelId, rooms }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [tariffDetailsOpen, setTariffDetailsOpen] = useState(false);
  const [selectedTariff, setSelectedTariff] = useState<TariffUpload | null>(null);
  const [comparisonOpen, setComparisonOpen] = useState(false);
  const [tariffForComparison, setTariffForComparison] = useState<TariffUpload | null>(null);
  const [extractionServiceAvailable, setExtractionServiceAvailable] = useState<boolean>(false);
  const [extractedData, setExtractedData] = useState<ExtractedTariffData[]>([]);

  const queryClient = useQueryClient();

  // Fetch tariff uploads with better error handling
  const {
    data: tariffs = [],
    isLoading,
    isError
  } = useQuery<TariffUpload[]>(
    ['tariffs', hotelId],
    () => fetchHotelTariffs(hotelId),
    {
      enabled: !!hotelId,
      retry: 1, // Only retry once to avoid excessive error logs
      onError: (err: any) => {
        console.error('Failed to fetch tariffs:', err);
      }
    }
  );

  // Set the first room as selected by default when rooms are loaded
  useEffect(() => {
    if (rooms?.length > 0 && !selectedRoom) {
      setSelectedRoom(rooms[0].hotelRoomId);
    }
  }, [rooms, selectedRoom]);

  // Check if tariff extraction service is available
  useEffect(() => {
    const checkService = async () => {
      const isAvailable = await checkTariffExtractionService();
      setExtractionServiceAvailable(isAvailable);
    };
    
    checkService();
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Check if file is a PDF
      if (file.type !== 'application/pdf') {
        toast.error('Only PDF files are supported');
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleTariffUpload = async () => {
    if (!selectedFile || !selectedRoom) {
      toast.error('Please select a room and tariff file');
      return;
    }

    setIsUploading(true);

    try {
      // 1. Generate a unique filename for the tariff
      const fileName = `${hotelId}-${selectedRoom}-${Date.now()}-${selectedFile.name}`;

      // 2. Upload the file
      const filePath = await handleFileUpload(
        'hotel-tariffs',
        selectedFile.type,
        fileName,
        selectedFile
      );

      // 3. Extract tariff data if service is available
      let extractedTariffData: ExtractedTariffData[] = [];
      if (extractionServiceAvailable) {
        try {
          extractedTariffData = await extractTariffFromPDF(selectedFile, true);
          setExtractedData(extractedTariffData);
        } catch (extractionError) {
          console.warn('Failed to extract tariff data, proceeding without extraction:', extractionError);
          toast.error('Could not extract tariff data automatically. You can still proceed with manual review.');
        }
      }

      // 4. Create tariff record with extracted data
      const newTariff = await createTariffUpload({
        hotelId,
        roomId: selectedRoom,
        filePath
      });

      // Add extracted data to the tariff if available
      if (newTariff && extractedTariffData.length > 0) {
        newTariff.priceData = extractedTariffData;
      }

      // 5. Reset form and refetch data
      setSelectedFile(null);
      const uploadInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      if (uploadInput) uploadInput.value = '';

      // 6. Refresh the tariffs data
      queryClient.invalidateQueries(['tariffs', hotelId]);

      // 7. Open the comparison view for the new tariff
      if (newTariff) {
        setTimeout(() => {
          showComparisonView(newTariff);
        }, 500);
      }

    } catch (error) {
      console.error('Error uploading tariff:', error);

      // Detailed error message
      if (error instanceof Error) {
        toast.error(`Error uploading tariff: ${error.message}`);
      } else {
        toast.error('Failed to upload tariff. Please try again.');
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleTariffAction = async (tariffId: string, action: 'approve' | 'reject') => {
    try {
      await updateTariffStatus(
        tariffId,
        action === 'approve' ? 'approved' : 'rejected'
      );
      queryClient.invalidateQueries(['tariffs', hotelId]);

      // Close the details dialog if it's open
      if (tariffDetailsOpen) {
        setTariffDetailsOpen(false);
      }
    } catch (error) {
      console.error(`Error ${action}ing tariff:`, error);
      toast.error(`Failed to ${action} tariff. Please try again.`);
    }
  };

  const handleTariffDelete = async (tariffId: string) => {
    try {
      await deleteTariffUpload(tariffId);
      queryClient.invalidateQueries(['tariffs', hotelId]);

      // Close the details dialog if it's open with the deleted tariff
      if (tariffDetailsOpen && selectedTariff?.tariffId === tariffId) {
        setTariffDetailsOpen(false);
      }

      // Close the comparison view if it's open with the deleted tariff
      if (comparisonOpen && tariffForComparison?.tariffId === tariffId) {
        setComparisonOpen(false);
      }
    } catch (error) {
      console.error('Error deleting tariff:', error);
      toast.error('Failed to delete tariff. Please try again.');
    }
  };

  const showTariffDetails = (tariff: TariffUpload) => {
    setSelectedTariff(tariff);
    setTariffDetailsOpen(true);
  };

  const showComparisonView = (tariff: TariffUpload) => {
    setTariffForComparison(tariff);
    setComparisonOpen(true);
  };

  // Function to get room name by ID
  const getRoomName = (roomId: string) => {
    const room = rooms?.find(r => r.hotelRoomId === roomId);
    return room ? room.hotelRoomType : 'Unknown Room';
  };

  // Mock extracted data for demonstration purposes
  // In a real implementation, this would come from the backend after processing the PDF
  const generateMockExtractedData = (): ExtractedTariffData[] => {
    if (!tariffForComparison) return [];

    // Find the room for this tariff
    const room = rooms.find(r => r.hotelRoomId === tariffForComparison.roomId);
    if (!room || !room.mealPlan || room.mealPlan.length === 0) return [];

    // Generate sample extracted data based on existing meal plans
    const result: ExtractedTariffData[] = [];

    // Group meal plans by type
    const mealPlanTypes = [...new Set(room.mealPlan.map(mp => mp.mealPlan))];
    const seasons = ['Peak Season', 'Off Season', 'Mid Season'];

    mealPlanTypes.forEach(type => {
      // Create sample date ranges (next 3 months)
      const today = new Date();
      const startDate1 = new Date(today);
      const endDate1 = new Date(today);
      endDate1.setMonth(endDate1.getMonth() + 1);

      const startDate2 = new Date(endDate1);
      startDate2.setDate(startDate2.getDate() + 1);
      const endDate2 = new Date(startDate2);
      endDate2.setMonth(endDate2.getMonth() + 1);

      // Add with slight price variations
      const baseMealPlan = room.mealPlan.find(mp => mp.mealPlan === type);
      if (baseMealPlan) {
        const basePrice = baseMealPlan.roomPrice;

        result.push({
          'Season': seasons[Math.floor(Math.random() * seasons.length)],
          'Start Date': startDate1.toISOString().split('T')[0],
          'End Date': endDate1.toISOString().split('T')[0],
          'Room Category': 'Deluxe Room',
          'Plan': type, // Using 'Plan' for LLM format
          'Room Price': basePrice + Math.floor(Math.random() * 500),
          'Adult Price': 0,
          'Child Price': 0,
          'Hotel': 'Sample Hotel',
          'Occupancy': 'Double',
          'Meal Plan': type, // Also include fallback format
          'Price': basePrice + Math.floor(Math.random() * 500)
        });

        result.push({
          'Season': seasons[Math.floor(Math.random() * seasons.length)],
          'Start Date': startDate2.toISOString().split('T')[0],
          'End Date': endDate2.toISOString().split('T')[0],
          'Room Category': 'Deluxe Room',
          'Plan': type, // Using 'Plan' for LLM format
          'Room Price': basePrice + Math.floor(Math.random() * 800),
          'Adult Price': 0,
          'Child Price': 0,
          'Hotel': 'Sample Hotel',
          'Occupancy': 'Double',
          'Meal Plan': type, // Also include fallback format
          'Price': basePrice + Math.floor(Math.random() * 800)
        });
      }
    });

    return result;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Service Status Alert */}
      <Alert className={extractionServiceAvailable ? "bg-green-50 border-green-200" : "bg-amber-50 border-amber-200"}>
        <Info className={`h-5 w-5 ${extractionServiceAvailable ? 'text-green-600' : 'text-amber-600'}`} />
        <AlertTitle className={extractionServiceAvailable ? "text-green-800" : "text-amber-800"}>
          {extractionServiceAvailable ? 'PDF Extraction Service Active' : 'PDF Extraction Service Unavailable'}
        </AlertTitle>
        <AlertDescription className={extractionServiceAvailable ? "text-green-700" : "text-amber-700"}>
          {extractionServiceAvailable 
            ? 'PDF data will be automatically extracted and compared with existing tariffs when you upload files.'
            : 'PDF extraction service is not running. Mock data will be used for demonstration. To enable extraction, start the backend service at http://localhost:5000'
          }
        </AlertDescription>
      </Alert>

      {/* Upload Form */}
      <div className="bg-white rounded-lg border p-5 shadow-sm">
        <h3 className="text-base font-semibold mb-4 text-gray-800">Upload New Tariff</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {/* Room Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Room
            </label>
            <select
              value={selectedRoom}
              onChange={(e) => setSelectedRoom(e.target.value)}
              className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">Select a room</option>
              {rooms?.map((room) => (
                <option key={room.hotelRoomId} value={room.hotelRoomId}>
                  {room.hotelRoomType}
                </option>
              ))}
            </select>
          </div>

          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tariff PDF
            </label>
            <div className="flex items-center gap-2">
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileChange}
                className="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-medium
                  file:bg-blue-50 file:text-blue-700
                  hover:file:bg-blue-100"
              />
            </div>
            {selectedFile && (
              <p className="mt-1 text-sm text-gray-500">
                {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
              </p>
            )}
          </div>
        </div>

        <Button
          onClick={handleTariffUpload}
          disabled={!selectedFile || !selectedRoom || isUploading}
          className="w-full md:w-auto"
        >
          {isUploading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Uploading...
            </>
          ) : (
            <>
              <Upload size={16} className="mr-2" />
              Upload Tariff
            </>
          )}
        </Button>
      </div>

      {/* Error Message */}
      {isError && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            There was an error fetching tariff data. The backend API might not be fully implemented yet.
          </AlertDescription>
        </Alert>
      )}

      {/* Tariff List */}
      <div>
        <h3 className="text-base font-medium mb-3 text-gray-700">Uploaded Tariffs</h3>

        {tariffs.length === 0 ? (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center text-gray-500">
            No tariff files have been uploaded yet.
          </div>
        ) : (
          <div className="bg-white border rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Room Type
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tariff File
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Upload Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {tariffs.map((tariff) => (
                  <tr key={tariff.tariffId} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {getRoomName(tariff.roomId)}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      <a
                        href={tariff.filePath.includes('mock')
                          ? '#'
                          : `https://tripemilestone.in-maa-1.linodeobjects.com/${tariff.filePath}`
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-blue-600 hover:underline"
                        onClick={(e) => {
                          if (tariff.filePath.includes('mock')) {
                            e.preventDefault();
                            toast.success('This is a demo file (not actually uploaded to server)');
                          }
                        }}
                      >
                        <FileText size={16} className="mr-1" />
                        {tariff.filePath.split('-').pop() || 'Demo-Tariff.pdf'}
                        {tariff.filePath.includes('mock') && <span className="ml-1 text-xs text-amber-600">(DEMO)</span>}
                      </a>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {format(new Date(tariff.uploadDate), 'MMM d, yyyy')}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {tariff.status === 'pending' && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                          Pending
                        </span>
                      )}
                      {tariff.status === 'approved' && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                          Approved
                        </span>
                      )}
                      {tariff.status === 'rejected' && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                          Rejected
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-sm text-right space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => showTariffDetails(tariff)}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        Details
                      </Button>

                      {tariff.status === 'pending' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => showComparisonView(tariff)}
                            className="text-blue-600 border-blue-200 hover:bg-blue-50"
                          >
                            Compare
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTariffAction(tariff.tariffId!, 'approve')}
                            className="text-green-600 border-green-200 hover:bg-green-50"
                          >
                            <Check size={16} className="mr-1" />
                            Approve
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTariffAction(tariff.tariffId!, 'reject')}
                            className="text-red-600 border-red-200 hover:bg-red-50"
                          >
                            <X size={16} className="mr-1" />
                            Reject
                          </Button>
                        </>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTariffDelete(tariff.tariffId!)}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        Delete
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Tariff Details Dialog */}
      {selectedTariff && (
        <Dialog open={tariffDetailsOpen} onOpenChange={setTariffDetailsOpen}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Tariff Details</DialogTitle>
              <DialogDescription>
                Details for {getRoomName(selectedTariff.roomId)}
                {selectedTariff.tariffId?.includes('mock') && <span className="ml-2 text-amber-600 text-xs">(DEMO MODE)</span>}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 my-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Room</h4>
                  <p className="text-sm">{getRoomName(selectedTariff.roomId)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <p className="text-sm">
                    {selectedTariff.status === 'pending' && 'Pending Review'}
                    {selectedTariff.status === 'approved' && 'Approved'}
                    {selectedTariff.status === 'rejected' && 'Rejected'}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Upload Date</h4>
                  <p className="text-sm">{format(new Date(selectedTariff.uploadDate), 'MMM d, yyyy')}</p>
                </div>
                {selectedTariff.approvalDate && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Approval Date</h4>
                    <p className="text-sm">{format(new Date(selectedTariff.approvalDate), 'MMM d, yyyy')}</p>
                  </div>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-500">Tariff File</h4>
                <a
                  href={selectedTariff.filePath.includes('mock')
                    ? '#'
                    : `https://tripemilestone.in-maa-1.linodeobjects.com/${selectedTariff.filePath}`
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 text-sm hover:underline flex items-center"
                  onClick={(e) => {
                    if (selectedTariff.filePath.includes('mock')) {
                      e.preventDefault();
                      toast.success('This is a demo file (not actually uploaded to server)');
                    }
                  }}
                >
                  <FileText size={16} className="mr-1" />
                  {selectedTariff.filePath.split('-').pop() || 'Demo-Tariff.pdf'}
                  {selectedTariff.filePath.includes('mock') && <span className="ml-1 text-xs text-amber-600">(DEMO)</span>}
                </a>
              </div>

              {selectedTariff.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Notes</h4>
                  <p className="text-sm">{selectedTariff.notes}</p>
                </div>
              )}

              {selectedTariff.priceData && selectedTariff.priceData.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Price Data</h4>
                  <div className="border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Meal Plan</th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Date Range</th>
                          <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">Price (₹)</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {selectedTariff.priceData.map((price, index) => (
                          <tr key={index}>
                            <td className="px-3 py-2 text-xs">{price['Plan'] || price['Meal Plan'] || 'N/A'}</td>
                            <td className="px-3 py-2 text-xs">
                              {(() => {
                                try {
                                  const startDate = new Date(price['Start Date']);
                                  const endDate = new Date(price['End Date']);
                                  return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
                                } catch (error) {
                                  return `${price['Start Date']} - ${price['End Date']}`;
                                }
                              })()}
                            </td>
                            <td className="px-3 py-2 text-xs text-right">{price['Room Price'] || price['Price'] || 'N/A'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {selectedTariff.status === 'pending' && (
                <Alert className="mt-4">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Processing Required</AlertTitle>
                  <AlertDescription>
                    This tariff needs to be processed and approved for prices to be updated.
                  </AlertDescription>
                </Alert>
              )}

              {selectedTariff.tariffId?.includes('mock') && (
                <Alert className="mt-4 bg-blue-50 border-blue-200">
                  <Info className="h-4 w-4 text-blue-500" />
                  <AlertTitle className="text-blue-800">Demo Mode</AlertTitle>
                  <AlertDescription className="text-blue-700">
                    This is a demo tariff record. In production, this would be stored in the database.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setTariffDetailsOpen(false)}>
                Close
              </Button>

              {selectedTariff.status === 'pending' && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setTariffDetailsOpen(false);
                      showComparisonView(selectedTariff);
                    }}
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    Compare
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleTariffAction(selectedTariff.tariffId!, 'approve');
                      setTariffDetailsOpen(false);
                    }}
                    className="text-green-600 border-green-200 hover:bg-green-50"
                  >
                    <Check size={16} className="mr-1" />
                    Approve
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleTariffAction(selectedTariff.tariffId!, 'reject');
                      setTariffDetailsOpen(false);
                    }}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <X size={16} className="mr-1" />
                    Reject
                  </Button>
                </>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Tariff Comparison Dialog */}
      {tariffForComparison && (
        <Dialog
          open={comparisonOpen}
          onOpenChange={setComparisonOpen}
        >
          <DialogContent className="max-w-5xl">
            <DialogHeader>
              <DialogTitle>Tariff Comparison</DialogTitle>
              <DialogDescription>
                Compare extracted tariff data with existing pricing
              </DialogDescription>
            </DialogHeader>

            <TariffComparison
              tariff={tariffForComparison}
              existingData={rooms.find(r => r.hotelRoomId === tariffForComparison.roomId)?.mealPlan || []}
              extractedData={
                tariffForComparison.priceData || 
                (extractionServiceAvailable ? extractedData : generateMockExtractedData())
              }
              roomName={getRoomName(tariffForComparison.roomId)}
              onApprove={() => {
                setComparisonOpen(false);
                queryClient.invalidateQueries(['tariffs', hotelId]);
              }}
              onReject={() => {
                setComparisonOpen(false);
                queryClient.invalidateQueries(['tariffs', hotelId]);
              }}
              onClose={() => setComparisonOpen(false)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default TariffUploadList;