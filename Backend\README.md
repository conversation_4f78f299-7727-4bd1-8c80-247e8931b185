# Tariff Extraction Backend Service

This backend service provides PDF tariff extraction capabilities using Mistral OCR and Google Gemini LLM.

## Setup Instructions

### 1. Install Dependencies

```bash
cd Backend
pip install -r requirements.txt
```

### 2. Environment Variables

Create a `.env` file in the Backend directory with the following variables:

```env
MISTRAL_API_KEY=your_mistral_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

### 3. API Keys Setup

#### Mistral API Key
1. Go to [Mistral AI Console](https://console.mistral.ai/)
2. Create an account or sign in
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your `.env` file

#### Google API Key (for Gemini)
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Create an account or sign in
3. Generate an API key
4. Copy the key to your `.env` file

### 4. Run the Service

```bash
python app.py
```

The service will start on `http://localhost:5000`

## API Endpoints

### POST /api/extract-tariff
Extracts tariff data from uploaded PDF file.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body:
  - `file`: PDF file to extract data from
  - `use_llm`: (optional) "true" or "false" - whether to use LLM extraction (default: true)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "Season": "Peak Season",
      "Start Date": "2024-04-15",
      "End Date": "2024-05-09",
      "Room Category": "Deluxe Room",
      "Plan": "MAP",
      "Room Price": 5000,
      "Adult Price": 0,
      "Child Price": 0
    }
  ],
  "count": 1
}
```

### GET /api/health
Health check endpoint.

**Response:**
```json
{
  "success": true,
  "message": "Tariff extraction service is running"
}
```

## Data Formats

The service supports two extraction methods:

### 1. LLM Extraction (use_llm=true)
Uses Google Gemini to extract structured data with columns:
- Room Category
- Plan (meal plan)
- Start Date
- End Date
- Room Price
- Adult Price
- Child Price
- Season

### 2. Fallback Extraction (use_llm=false)
Uses pattern matching with columns:
- Hotel
- Room Category
- Occupancy
- Meal Plan
- Season
- Start Date
- End Date
- Price

## Integration with Frontend

The frontend automatically detects if this service is running and:
- Shows a green status indicator when service is available
- Automatically extracts tariff data when PDFs are uploaded
- Falls back to mock data when service is unavailable

## Troubleshooting

### Service Not Starting
- Check that all dependencies are installed: `pip install -r requirements.txt`
- Verify API keys are correctly set in `.env` file
- Check that port 5000 is not in use by another service

### Extraction Errors
- Ensure PDF files are not corrupted or password-protected
- Check API key limits and quotas
- Review the console output for detailed error messages

### CORS Issues
- The service includes CORS headers for frontend integration
- If you change the frontend port, update the CORS configuration in `app.py`