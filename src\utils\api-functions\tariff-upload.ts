import api from './auth';
import toast from 'react-hot-toast';
import { TariffUpload, TariffPriceData, ExtractedTariffData } from '@/types/types';

// Mock data for development while backend API is not available
const mockTariffs: TariffUpload[] = [];

// Fetch all tariff uploads for a hotel
export async function fetchHotelTariffs(hotelId: string) {
  try {
    try {
      const response = await api.get(`admin/hotel/${hotelId}/tariffs`);
      return Promise.resolve(response.data.result);
    } catch (apiError: any) {
      // If the endpoint returns 404, it means the API is not yet implemented
      // Return mock data instead of rejecting so the UI doesn't break
      if (apiError.response && apiError.response.status === 404) {
        console.log('Tariff API not yet implemented, returning mock data');
        return Promise.resolve(mockTariffs);
      }
      throw apiError;
    }
  } catch (error) {
    console.error('Error fetching tariff data:', error);
    toast.error('Error fetching tariff data');
    // Return empty array instead of rejecting to prevent UI from breaking
    return Promise.resolve([]);
  }
}

// Create a new tariff upload
export async function createTariffUpload(tariffData: Omit<TariffUpload, 'tariffId' | 'uploadDate' | 'status'>) {
  try {
    try {
      const response = await api.post('admin/hotel/tariff', tariffData);
      toast.success('Tariff uploaded successfully');
      return Promise.resolve(response.data.result);
    } catch (apiError: any) {
      // If the endpoint returns 404, it means the API is not yet implemented
      if (apiError.response && apiError.response.status === 404) {
        console.log('Tariff API not yet implemented, simulating successful upload');
        
        // Create a mock tariff entry
        const mockTariff: TariffUpload = {
          tariffId: `mock-${Date.now()}`,
          hotelId: tariffData.hotelId,
          roomId: tariffData.roomId,
          filePath: tariffData.filePath,
          uploadDate: new Date().toISOString(),
          status: 'pending'
        };
        
        // Add it to our mock data
        mockTariffs.push(mockTariff);
        
        toast.success('Tariff uploaded successfully (DEMO)');
        return Promise.resolve(mockTariff);
      }
      throw apiError;
    }
  } catch (error) {
    console.error('Error uploading tariff:', error);
    toast.error('Error uploading tariff');
    return Promise.reject(error);
  }
}

// Update tariff status (approve/reject)
export async function updateTariffStatus(
  tariffId: string, 
  status: 'approved' | 'rejected', 
  priceData?: TariffPriceData[] | ExtractedTariffData[],
  notes?: string
) {
  try {
    try {
      const response = await api.put(`admin/hotel/tariff/${tariffId}`, {
        status,
        priceData,
        notes
      });
      
      const actionText = status === 'approved' ? 'approved' : 'rejected';
      toast.success(`Tariff ${actionText} successfully`);
      return Promise.resolve(response.data.result);
    } catch (apiError: any) {
      // If the endpoint returns 404, it means the API is not yet implemented
      if (apiError.response && apiError.response.status === 404) {
        console.log('Tariff API not yet implemented, simulating update');
        
        // Convert TariffPriceData to ExtractedTariffData if needed
        const convertedPriceData: ExtractedTariffData[] | undefined = priceData ? 
          priceData.map(item => {
            // Check if it's already ExtractedTariffData
            if ('Meal Plan' in item || 'Season' in item) {
              return item as ExtractedTariffData;
            }
            // Convert from TariffPriceData
            const tariffItem = item as TariffPriceData;
            return {
              'Meal Plan': tariffItem.mealPlanType,
              'Season': 'Peak Season', // Default season
              'Start Date': tariffItem.startDate,
              'End Date': tariffItem.endDate,
              'Price': tariffItem.roomPrice,
              'Room Price': tariffItem.roomPrice,
              'Adult Price': 0,
              'Child Price': 0
            } as ExtractedTariffData;
          }) : undefined;

        // Update the mock tariff
        const mockTariffIndex = mockTariffs.findIndex(t => t.tariffId === tariffId);
        if (mockTariffIndex !== -1) {
          mockTariffs[mockTariffIndex] = {
            ...mockTariffs[mockTariffIndex],
            status,
            priceData: convertedPriceData,
            notes,
            approvalDate: new Date().toISOString(),
            approvedBy: 'Admin User'
          };
        }
        
        const actionText = status === 'approved' ? 'approved' : 'rejected';
        toast.success(`Tariff ${actionText} successfully (DEMO)`);
        return Promise.resolve(mockTariffs[mockTariffIndex]);
      }
      throw apiError;
    }
  } catch (error) {
    console.error(`Error ${status === 'approved' ? 'approving' : 'rejecting'} tariff:`, error);
    toast.error(`Error ${status === 'approved' ? 'approving' : 'rejecting'} tariff`);
    return Promise.reject(error);
  }
}

// Delete a tariff upload
export async function deleteTariffUpload(tariffId: string) {
  try {
    try {
      const response = await api.delete(`admin/hotel/tariff/${tariffId}`);
      toast.success('Tariff deleted successfully');
      return Promise.resolve(response.data.result);
    } catch (apiError: any) {
      // If the endpoint returns 404, it means the API is not yet implemented
      if (apiError.response && apiError.response.status === 404) {
        console.log('Tariff API not yet implemented, simulating delete');
        
        // Remove from mock tariffs
        const index = mockTariffs.findIndex(t => t.tariffId === tariffId);
        if (index !== -1) {
          mockTariffs.splice(index, 1);
        }
        
        toast.success('Tariff deleted successfully (DEMO)');
        return Promise.resolve({ success: true });
      }
      throw apiError;
    }
  } catch (error) {
    console.error('Error deleting tariff:', error);
    toast.error('Error deleting tariff');
    return Promise.reject(error);
  }
} 