from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import tempfile
from extract_tariff import extract_tariff_from_pdf
import traceback

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/api/extract-tariff', methods=['POST'])
def extract_tariff():
    """
    API endpoint to extract tariff data from uploaded PDF file.
    Expects a PDF file in the request and returns extracted tariff data.
    """
    try:
        # Check if file is present in request
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Check if file is PDF
        if not file.filename.lower().endswith('.pdf'):
            return jsonify({
                'success': False,
                'error': 'Only PDF files are supported'
            }), 400
        
        # Get optional parameters
        use_llm = request.form.get('use_llm', 'true').lower() == 'true'
        
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            # Extract tariff data using the extract_tariff.py function
            extracted_data = extract_tariff_from_pdf(
                pdf_path=temp_path,
                use_llm=use_llm
            )
            
            # Clean up temporary file
            os.unlink(temp_path)
            
            if not extracted_data:
                return jsonify({
                    'success': False,
                    'error': 'No tariff data could be extracted from the PDF'
                }), 400
            
            return jsonify({
                'success': True,
                'data': extracted_data,
                'count': len(extracted_data)
            })
            
        except Exception as e:
            # Clean up temporary file in case of error
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise e
            
    except Exception as e:
        print(f"Error extracting tariff: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'Error processing PDF: {str(e)}'
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'Tariff extraction service is running'
    })

if __name__ == '__main__':
    # Create output directory if it doesn't exist
    os.makedirs('output', exist_ok=True)
    
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)